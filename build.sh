#!/bin/sh

# ==========================================
# LobeChat Admin Docker 构建推送脚本
# ==========================================

set -e  # 遇到错误立即退出

# 固定配置
IMAGE_NAME="ghcr.io/mocha-fe/zephyr"
VERSION="latest"
PLATFORM="linux/amd64,linux/arm64"

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# 检查必需文件
for file in Dockerfile package.json next.config.ts; do
    if [ ! -f "$file" ]; then
        log_error "必需文件不存在: $file"
        exit 1
    fi
done

# 检查构建产物
if [ ! -d ".next/standalone" ]; then
    log_error "构建产物不存在，请先运行本地构建："
    log_error "  sh build-local.sh"
    exit 1
fi

log_info "开始构建并推送 Zephyr 镜像"
log_info "目标镜像: ${IMAGE_NAME}:${VERSION}"
log_info "目标平台: ${PLATFORM}"

# 执行多平台构建并推送
log_info "构建并推送镜像..."
docker buildx build \
    --platform ${PLATFORM} \
    --push \
    -t "${IMAGE_NAME}:${VERSION}" \
    .

log_success "镜像已成功构建并推送到: ${IMAGE_NAME}:${VERSION}"

# 显示运行建议
echo ""
echo "镜像已推送完成! 运行建议:"
echo ""
echo "1. 使用docker-compose运行 (推荐):"
echo "   在您的docker-compose.yml中配置环境变量映射"
echo ""
echo "2. 或直接运行容器:"
echo "   docker run -d \\"
echo "     --name zephyr \\"
echo "     -p 3000:3000 \\"
echo "     -e CLERK_SECRET_KEY=your_key \\"
echo "     -e DATABASE_URL=your_db_url \\"
echo "     --restart unless-stopped \\"
echo "     ${IMAGE_NAME}:${VERSION}"
echo ""
echo "3. 查看日志:"
echo "   docker logs -f zephyr"
echo ""

log_success "完成！"
