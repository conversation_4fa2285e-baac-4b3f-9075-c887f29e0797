import { Typography } from 'antd';
import { createStyles } from 'antd-style';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import FileIcon from '@/components/FileIcon';
import { FileItem as FileItemType } from '@/services/files';
import { formatSize } from '@/utils/format';

const useStyles = createStyles(({ css, token, isDarkMode }) => ({
  container: css`
    cursor: pointer;

    overflow: hidden;

    max-width: 420px;
    padding-block: 8px;
    padding-inline: 12px 32px;
    border: 1px solid ${isDarkMode ? token.colorBorder : token.colorSplit};
    border-radius: 8px;

    &:hover {
      background: ${token.colorFillTertiary};
    }
  `,
}));

const FileItem = memo<FileItemType>(({ id, fileType, size, filename }) => {
  const { styles } = useStyles();

  return (
    <Flexbox className={styles.container} gap={12} horizontal key={id}>
      <FileIcon fileName={filename} fileType={fileType} />
      <Flexbox style={{ overflow: 'hidden' }}>
        <Typography.Text ellipsis>{filename}</Typography.Text>
        <Typography.Text type={'secondary'}>{formatSize(size)}</Typography.Text>
      </Flexbox>
    </Flexbox>
  );
});
export default FileItem;
