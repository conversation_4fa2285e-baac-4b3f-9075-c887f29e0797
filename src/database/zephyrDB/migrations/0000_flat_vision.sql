CREATE TABLE "agent_suggestions" (
	"id" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "agent_suggestions_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"suggestion" jsonb NOT NULL,
	"topic_id" text NOT NULL,
	"parent_message_id" text NOT NULL,
	"accessed_at" timestamp with time zone DEFAULT now() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "customer_sessions" (
	"id" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "customer_sessions_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"session_id" text NOT NULL,
	"gender" varchar(10),
	"age" integer,
	"position" varchar(100),
	"phone" varchar(20),
	"email" varchar(255),
	"wechat" varchar(50),
	"company" varchar(200),
	"industry" varchar(100),
	"scale" varchar(50),
	"address" text,
	"chat_config" jsonb,
	"accessed_at" timestamp with time zone DEFAULT now() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "customer_sessions_session_id_unique" UNIQUE("session_id"),
	CONSTRAINT "unique_session" UNIQUE("session_id")
);
--> statement-breakpoint
CREATE INDEX "agent_suggestions_topic_id_idx" ON "agent_suggestions" USING btree ("topic_id");--> statement-breakpoint
CREATE INDEX "agent_suggestions_created_at_idx" ON "agent_suggestions" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "customer_sessions_session_id_idx" ON "customer_sessions" USING btree ("session_id");