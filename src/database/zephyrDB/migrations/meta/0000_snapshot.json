{"id": "46de224b-f5f1-41ac-8f6b-a00bc824c48e", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.agent_suggestions": {"name": "agent_suggestions", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "agent_suggestions_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "suggestion": {"name": "suggestion", "type": "jsonb", "primaryKey": false, "notNull": true}, "topic_id": {"name": "topic_id", "type": "text", "primaryKey": false, "notNull": true}, "parent_message_id": {"name": "parent_message_id", "type": "text", "primaryKey": false, "notNull": true}, "accessed_at": {"name": "accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"agent_suggestions_topic_id_idx": {"name": "agent_suggestions_topic_id_idx", "columns": [{"expression": "topic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_suggestions_created_at_idx": {"name": "agent_suggestions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_sessions": {"name": "customer_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "customer_sessions_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "age": {"name": "age", "type": "integer", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "wechat": {"name": "wechat", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "scale": {"name": "scale", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "chat_config": {"name": "chat_config", "type": "jsonb", "primaryKey": false, "notNull": false}, "accessed_at": {"name": "accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"customer_sessions_session_id_idx": {"name": "customer_sessions_session_id_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"customer_sessions_session_id_unique": {"name": "customer_sessions_session_id_unique", "nullsNotDistinct": false, "columns": ["session_id"]}, "unique_session": {"name": "unique_session", "nullsNotDistinct": false, "columns": ["session_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}