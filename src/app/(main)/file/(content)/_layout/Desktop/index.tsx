import { Flexbox } from 'react-layout-kit';

import { LayoutProps } from '../type';
import Container from './Container';

const Layout = ({ children, modal }: LayoutProps) => {
  return (
    <>
      <Flexbox
        height={'100%'}
        horizontal
        style={{ maxWidth: '100%', overflow: 'hidden', position: 'relative' }}
        width={'100%'}
      >
        <Container>{children}</Container>
      </Flexbox>
      {modal}
    </>
  );
};

Layout.displayName = 'DesktopFileLayout';

export default Layout;
