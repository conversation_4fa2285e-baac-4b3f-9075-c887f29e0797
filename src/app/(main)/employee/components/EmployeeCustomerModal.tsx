import React from 'react';
import { Tabs, Checkbox } from 'antd';
import { <PERSON><PERSON>, Modal , Tooltip } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import SkeletonList from './SkeletonList';
import { DoubleRightOutlined, DoubleLeftOutlined } from '@ant-design/icons';


interface Customer {
  id: string;
  customerName: string;
  employeeName?: string;
  username?: string;
}

interface EmployeeCustomerModalProps {
  open: boolean;
  onClose: () => void;
  employee: any;
  sessionList: Customer[];
  employeeCustomers: string[];
  onSave: () => void;
  loading: boolean;
  customerTab: string;
  setCustomerTab: (tab: string) => void;
  selectedLeft: string[];
  setSelectedLeft: (ids: string[]) => void;
  selectedRight: string[];
  setSelectedRight: (ids: string[]) => void;
  moveToRight: () => void;
  moveToLeft: () => void;
  leftList: Customer[];
  rightList: Customer[];
}

const useStyles = createStyles(({ css, token }) => ({
  modal: css`
    .ant-modal-content {
      border-radius: 8px;
      padding: 24px;
    }
    .ant-modal-body {
      padding-block: 0 !important;
      padding-inline: 0 !important;
    }
  `,
  modalBody: css`
    border-radius: 8px;
    padding: 24px;
    height: 500px;
    display: flex;
    flex-direction: column;
  `,
  title: css`
    font-size: 24px;
    font-weight: 400;
    margin-bottom: 8px;
    color: ${token.colorText};
  `,
  mainContainer: css`
    display: flex;
    flex: 1;
    min-height: 0;
  `,
  leftPanel: css`
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  `,
  rightPanel: css`
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  `,
  panelContainer: css`
    background: ${token.colorFillQuaternary};
    flex: 1;
    border-radius: 6px;
    overflow: auto;
    border: 1px solid ${token.colorBorder};
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 8px;
  `,
  panelHeader: css`
    display: flex;
    align-items: center;
    border-bottom: 1px solid ${token.colorBorder};
    padding: 0 12px;
    height: 40px;
    font-weight: 500;
    flex-shrink: 0;
  `,
  panelContent: css`
    flex: 1;
    overflow: auto;
  `,
  customerItem: css`
    display: flex;
    align-items: center;
    border-bottom: 1px solid ${token.colorBorder};
    padding: 0 12px;
    height: 40px;
  `,
  arrowContainer: css`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 16px;
    width: 60px;
  `,
  arrowButton: css`
    width: 36px;
    height: 36px;
    border: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgContainer};
    color: ${token.colorText};
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  `,
  footer: css`
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 8px;
  `,
  panelTitle: css`
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 4px;
    height: 38px;
    line-height: 38px;
    flex-shrink: 0;
    color: ${token.colorText};
  `,
  panelItems: css`
    flex: 1;
    width: 50%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 10px 8px;
  `,
}));

const EmployeeCustomerModal: React.FC<EmployeeCustomerModalProps> = ({
  open,
  onClose,
  sessionList,
  onSave,
  loading,
  customerTab,
  setCustomerTab,
  selectedLeft,
  setSelectedLeft,
  selectedRight,
  setSelectedRight,
  moveToRight,
  moveToLeft,
  leftList,
  rightList,
}) => {
  const { styles } = useStyles();

  return (
    <Modal
      className={styles.modal}
      classNames={{
        body: styles.modalBody,
      }}
      closable={false}
      footer={null}
      onCancel={onClose}
      open={open}
      style={{ top: 60 }}
      title={null}
      width={680}
    >
      {loading && sessionList.length === 0 ? (
        <SkeletonList />
      ) : (
        <>
          <div className={styles.title}>员工对接客户管理</div>
          <div className={styles.mainContainer}>
            {/* 左侧 */}
            <div className={styles.leftPanel}>
              <Tabs
                activeKey={customerTab}
                items={[
                  { key: 'all', label: '全部客户' },
                  { key: 'unassigned', label: '未分配客户' },
                ]}
                onChange={setCustomerTab}
                tabBarStyle={{
                  height: 38,
                  marginBottom: 4,
                  borderBottom: 'none',
                }}
              />
              <div className={styles.panelContainer}>
                {/* 标题栏 */}
                <div className={styles.panelHeader}>
                  <Checkbox
                    checked={
                      selectedLeft.length === leftList.length &&
                      leftList.length > 0
                    }
                    indeterminate={
                      selectedLeft.length > 0 &&
                      selectedLeft.length < leftList.length
                    }
                    onChange={(e) =>
                      setSelectedLeft(
                        e.target.checked ? leftList.map((c) => c.id) : []
                      )
                    }
                    style={{ marginRight: 8 }}
                  />
                  <Tooltip title='客户名称'>
                    <div
                      className={styles.panelItems}
                      style={{ fontWeight: 500 }}
                    >
                      客户名称
                    </div>
                  </Tooltip>
                  <Tooltip title='对接人'>
                    <div
                      className={styles.panelItems}
                      style={{ fontWeight: 500 }}
                    >
                      对接人
                    </div>
                  </Tooltip>
                </div>
                {/* 客户项 */}
                <div className={styles.panelContent}>
                  {leftList.map((c) => (
                    <div className={styles.customerItem} key={c.id}>
                      <Checkbox
                        checked={selectedLeft.includes(c.id)}
                        onChange={(e) => {
                          setSelectedLeft(
                            e.target.checked
                              ? [...selectedLeft, c.id]
                              : selectedLeft.filter((id) => id !== c.id)
                          );
                        }}
                        style={{ marginRight: 8 }}
                      />
                      <Tooltip title={c.customerName}>
                        <div className={styles.panelItems}>
                          {c.customerName}
                        </div>
                      </Tooltip>
                      <Tooltip title={c.employeeName}>
                        <div className={styles.panelItems}>
                          {c.employeeName}
                        </div>
                      </Tooltip>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            {/* 中间双箭头 */}
            <div className={styles.arrowContainer}>
              <Button
                className={styles.arrowButton}
                disabled={selectedLeft.length === 0}
                icon={<DoubleRightOutlined />}
                onClick={moveToRight}
                shadow
              />
              <Button
                className={styles.arrowButton}
                disabled={selectedRight.length === 0}
                icon={<DoubleLeftOutlined />}
                onClick={moveToLeft}
                shadow
              />
            </div>
            {/* 右侧 */}
            <div className={styles.rightPanel}>
              <div className={styles.panelTitle}>员工客户</div>
              <div className={styles.panelContainer}>
                {/* 标题栏 */}
                <div className={styles.panelHeader}>
                  <Checkbox
                    checked={
                      selectedRight.length === rightList.length &&
                      rightList.length > 0
                    }
                    indeterminate={
                      selectedRight.length > 0 &&
                      selectedRight.length < rightList.length
                    }
                    onChange={(e) =>
                      setSelectedRight(
                        e.target.checked ? rightList.map((c) => c.id) : []
                      )
                    }
                    style={{ marginRight: 8 }}
                  />
                  <Tooltip title='客户名称'>
                    <div
                      className={styles.panelItems}
                      style={{ fontWeight: 500 }}
                    >
                      客户名称
                    </div>
                  </Tooltip>
                </div>
                {/* 客户项 */}
                <div className={styles.panelContent}>
                  {rightList.map((c) => (
                    <div className={styles.customerItem} key={c.id}>
                      <Checkbox
                        checked={selectedRight.includes(c.id)}
                        onChange={(e) => {
                          setSelectedRight(
                            e.target.checked
                              ? [...selectedRight, c.id]
                              : selectedRight.filter((id) => id !== c.id)
                          );
                        }}
                        style={{ marginRight: 8 }}
                      />
                      <Tooltip title={c.customerName}>
                        <div className={styles.panelItems}>
                          {c.customerName}
                        </div>
                      </Tooltip>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          {/* 底部按钮 */}
          <div className={styles.footer}>
            <Button onClick={onClose} shadow>
              取消
            </Button>
            <Button loading={loading} onClick={onSave} shadow type='primary'>
              保存
            </Button>
          </div>
        </>
      )}
    </Modal>
  );
};

export default EmployeeCustomerModal;
