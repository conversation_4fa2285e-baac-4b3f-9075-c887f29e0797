import React, { ReactNode, memo } from 'react';
import { Center, Flexbox } from 'react-layout-kit';

import InitProgress, { StageItem } from '@/components/InitProgress';

interface FullscreenLoadingProps {
  activeStage: number;
  contentRender?: ReactNode;
  stages: StageItem[];
}

const FullscreenLoading = memo<FullscreenLoadingProps>(
  ({ activeStage, stages, contentRender }) => {
    return (
      <Flexbox
        height={'100%'}
        style={{ position: 'relative', userSelect: 'none' }}
        width={'100%'}
      >
        <Center flex={1} gap={16} width={'100%'}>
          {contentRender ? (
            contentRender
          ) : (
            <InitProgress activeStage={activeStage} stages={stages} />
          )}
        </Center>
      </Flexbox>
    );
  }
);

export default FullscreenLoading;
